import axios from 'axios';
import { EventEmitter } from 'events';
import { Logger } from '../utils/Logger.js';
import { SessionManager } from './SessionManager.js';
import { PersistenceService } from './PersistenceService.js';
import { AntiBanService } from './AntiBanService.js';
import fs from 'fs/promises';
import path from 'path';
import cron from 'node-cron';

/**
 * WhatsApp HTTP Service - Integração com WPPConnect Server
 * Substitui a biblioteca WPPConnect por chamadas HTTP para o WPPConnect Server
 */
export class WhatsAppHttpService extends EventEmitter {
  constructor() {
    super();
    
    // Configurações básicas
    this.serverUrl = process.env.WPPCONNECT_SERVER_URL || 'http://localhost:21465';
    this.sessionName = process.env.WHATSAPP_SESSION_NAME || 'vereadora-rafaela';
    this.secretKey = process.env.WPPCONNECT_SECRET_KEY || 'vereadora-rafaela-secret-2024';
    
    // Estado da conexão
    this.isConnected = false;
    this.qrCode = null;
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 5;
    this.isSyncing = false;
    this.syncProgress = null;
    
    // Serviços auxiliares
    this.logger = new Logger();
    this.sessionManager = new SessionManager();
    this.persistenceService = new PersistenceService();
    this.antiBanService = new AntiBanService();
    
    // Cliente HTTP configurado
    this.httpClient = axios.create({
      baseURL: this.serverUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.secretKey}`
      }
    });
    
    // Configurações de reconexão
    this.reconnectInterval = null;
    this.reconnectDelay = 30000; // 30 segundos
    this.maxReconnectDelay = 300000; // 5 minutos
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    
    // Configurações de monitoramento
    this.healthCheckInterval = null;
    this.healthCheckDelay = 60000; // 1 minuto
    
    // Configurações de backup
    this.autoBackupEnabled = process.env.AUTO_BACKUP_ENABLED === 'true';
    this.backupInterval = process.env.BACKUP_INTERVAL || '0 2 * * *'; // Todo dia às 2h
    
    // Callbacks para eventos
    this.onQRCode = null;
    this.onStatusChange = null;
    this.onMessage = null;
    this.onReady = null;
    this.onDisconnected = null;
  }

  async initialize() {
    this.logger.info('🔄 Inicializando WhatsApp HTTP Service...');

    try {
      // Verificar se o WPPConnect Server está rodando
      await this.checkServerHealth();

      // Verificar se já existe uma sessão ativa
      const existingSessionStatus = await this.checkExistingSession();

      if (existingSessionStatus && (
        existingSessionStatus.status === 'isLogged' ||
        existingSessionStatus.status === 'CONNECTED'
      )) {
        this.logger.info('✅ Sessão já está conectada!');
        this.isConnected = true;
        this.connectionAttempts++;

        // Configurar callbacks se necessário
        if (this.onReady) {
          this.onReady();
        }

        if (this.onStatusChange) {
          this.onStatusChange('connected');
        }

        this.emit('ready');

      } else {
        // Tentar carregar sessão persistida
        await this.loadPersistedSession();

        // Inicializar sessão no servidor
        await this.initializeSession();
      }

      // Configurar monitoramento
      this.setupHealthMonitoring();

      // Configurar backup automático
      if (this.autoBackupEnabled) {
        this.setupAutoBackup();
      }

      return true;
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar WhatsApp HTTP Service:', error.message);

      // Não falhar completamente se o WPPConnect Server não estiver disponível
      this.logger.warn('⚠️ WhatsApp Service funcionará em modo degradado');

      // Tentar reconexão automática
      this.scheduleReconnect();

      // Retornar false em vez de throw para não quebrar a inicialização
      return false;
    }
  }

  async checkServerHealth() {
    try {
      const response = await this.httpClient.get('/api/health');
      this.logger.info('✅ WPPConnect Server está rodando:', response.data);
      return true;
    } catch (error) {
      this.logger.error('❌ WPPConnect Server não está acessível:', error.message);
      throw new Error('WPPConnect Server não está acessível');
    }
  }

  async checkExistingSession() {
    try {
      const response = await this.httpClient.get(`/api/${this.sessionName}/status`);
      return response.data;
    } catch (error) {
      this.logger.warn('⚠️ Erro ao verificar sessão existente:', error.message);
      return null;
    }
  }

  async initializeSession() {
    try {
      this.logger.info(`🔄 Inicializando sessão: ${this.sessionName}`);

      // Verificar se a sessão já existe e está conectada
      const sessionStatus = await this.getSessionStatus();

      // Verificar se já está conectada (isLogged, CONNECTED, etc.)
      if (sessionStatus && (
        sessionStatus.status === 'isLogged' ||
        sessionStatus.status === 'CONNECTED' ||
        sessionStatus.connected === true
      )) {
        this.logger.info('✅ Sessão já está conectada, não precisa de QR Code');
        this.isConnected = true;
        this.connectionAttempts++;

        // Forçar status conectado para evitar problemas de sincronização
        this.logger.info('🔧 Forçando status conectado para evitar problemas de sincronização');

        // Configurar callbacks se necessário
        if (this.onReady) {
          this.onReady();
        }

        if (this.onStatusChange) {
          this.onStatusChange('connected');
        }

        this.emit('ready');
        return;
      }

      // Iniciar nova sessão apenas se não estiver conectada
      this.logger.info('🔄 Sessão não está conectada, iniciando nova sessão...');
      const response = await this.httpClient.post(`/api/${this.sessionName}/start-session`, {
        webhook: process.env.WEBHOOK_URL,
        waitQrCode: true
      });

      this.logger.info('🔄 Sessão iniciada, aguardando QR Code...');

      // Aguardar QR Code apenas se necessário (não bloquear se falhar)
      try {
        await this.waitForQRCode();
      } catch (error) {
        this.logger.warn('⚠️ Não foi possível obter QR Code, mas sessão foi iniciada:', error.message);
        // Continuar mesmo sem QR code - pode ser que a sessão já esteja conectada
      }

    } catch (error) {
      this.logger.error('❌ Erro ao inicializar sessão:', error);
      throw error;
    }
  }

  async getSessionStatus() {
    try {
      const response = await this.httpClient.get(`/api/${this.sessionName}/status`);
      const data = response.data;

      // Log do status para debug
      this.logger.debug(`📊 Status da sessão ${this.sessionName}: ${data.status || 'unknown'}`);

      // Normalizar o status para incluir a propriedade connected
      const isConnected = data.status === 'isLogged' ||
                         data.status === 'CONNECTED' ||
                         data.connected === true;

      return {
        ...data,
        connected: isConnected
      };
    } catch (error) {
      this.logger.warn('⚠️ Erro ao obter status da sessão:', error.message);
      return {
        connected: false,
        status: 'disconnected',
        error: error.message
      };
    }
  }

  async waitForQRCode() {
    const maxAttempts = 10; // 10 tentativas (10 segundos) - reduzido para não travar
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        // Primeiro verificar se a sessão já está conectada
        const sessionStatus = await this.getSessionStatus();

        if (sessionStatus && (
          sessionStatus.status === 'isLogged' ||
          sessionStatus.status === 'CONNECTED'
        )) {
          this.logger.info('✅ Sessão conectada durante aguardo do QR Code');
          this.isConnected = true;
          this.emit('ready');
          return;
        }

        // Tentar obter QR Code (testando diferentes endpoints)
        let response;
        try {
          response = await this.httpClient.get(`/api/${this.sessionName}/qr-code`);
        } catch (error) {
          // Tentar endpoint alternativo
          try {
            response = await this.httpClient.get(`/api/${this.sessionName}/qr`);
          } catch (error2) {
            // Tentar endpoint de status que pode incluir QR
            response = await this.httpClient.get(`/api/${this.sessionName}/status`);
          }
        }

        // Verificar se há QR code na resposta (diferentes formatos possíveis)
        const qrCodeData = response.data.qrCode || response.data.qr || response.data.base64 || response.data.qrcode;

        if (qrCodeData) {
          this.qrCode = {
            base64: qrCodeData,
            timestamp: new Date().toISOString(),
            attempt: attempts + 1
          };

          this.logger.info('📱 QR Code obtido com sucesso');
          this.emit('qr', this.qrCode.base64);

          if (this.onQRCode) {
            await this.onQRCode(this.qrCode.base64, null);
          }

          return;
        }

        // Aguardar 1 segundo antes da próxima tentativa
        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;

      } catch (error) {
        this.logger.warn(`⚠️ Tentativa ${attempts + 1} de obter QR Code falhou:`, error.message);
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new Error('Timeout ao aguardar QR Code');
  }

  async sendMessage(to, message, options = {}) {
    try {
      // Verificar status real do servidor antes de enviar
      const serverStatus = await this.getSessionStatus();
      if (serverStatus && serverStatus.connected && !this.isConnected) {
        this.logger.info('✅ Sincronizando status: servidor conectado');
        this.isConnected = true;
      }

      if (!this.isConnected) {
        throw new Error('WhatsApp não está conectado');
      }

      this.logger.info(`📤 Preparando envio para ${to}: ${message.substring(0, 50)}...`);

      // Usar sistema anti-ban para envio seguro (se disponível)
      let antiBanResult = { delay: 0, riskLevel: 0 };
      if (this.antiBanService) {
        antiBanResult = this.antiBanService.calculateDelay(to, message);

        if (antiBanResult.delay > 0) {
          this.logger.info(`⏱️ Aguardando ${antiBanResult.delay}ms por segurança anti-ban`);
          await new Promise(resolve => setTimeout(resolve, antiBanResult.delay));
        }
      }

      // Enviar mensagem via API
      const response = await this.httpClient.post(`/api/${this.sessionName}/send-message`, {
        phone: to,
        message: message,
        ...options
      });

      this.logger.info(`✅ Mensagem enviada: ${response.data.id || 'success'} (risco: ${antiBanResult.riskLevel}%)`);

      return {
        id: response.data.id || `msg_${Date.now()}`,
        riskLevel: antiBanResult.riskLevel,
        delay: antiBanResult.delay,
        antiBanActive: !!this.antiBanService
      };

    } catch (error) {
      this.logger.error('❌ Erro ao enviar mensagem:', error);
      throw error;
    }
  }

  async checkNumberStatus(number) {
    try {
      const response = await this.httpClient.post(`/api/${this.sessionName}/check-number-status`, {
        phone: number
      });
      
      return {
        canReceiveMessage: response.data.canReceiveMessage || false,
        exists: response.data.exists || false
      };
    } catch (error) {
      this.logger.warn('⚠️ Erro ao verificar número:', error.message);
      return { canReceiveMessage: false, exists: false };
    }
  }

  async getChats() {
    try {
      if (!this.isConnected) {
        throw new Error('WhatsApp não está conectado');
      }

      const response = await this.httpClient.get(`/api/${this.sessionName}/all-chats`);
      return response.data;

    } catch (error) {
      this.logger.error('❌ Erro ao obter chats:', error);
      throw error;
    }
  }

  // Método para obter conversas sincronizadas (formatadas para o frontend)
  async getSyncedChats() {
    try {
      if (!this.isConnected) {
        this.logger.warn('⚠️ WhatsApp não está conectado para obter chats');
        return [];
      }

      const response = await this.httpClient.get(`/api/${this.sessionName}/all-chats`);
      const chats = response.data || [];

      // Formatar os chats para o frontend
      const formattedChats = chats.map(chat => ({
        id: chat.id._serialized || chat.id,
        name: chat.name || chat.contact?.name || chat.contact?.pushname || 'Contato sem nome',
        lastMessage: chat.lastMessage ? {
          body: chat.lastMessage.body || '',
          timestamp: chat.lastMessage.timestamp || Date.now(),
          fromMe: chat.lastMessage.fromMe || false
        } : null,
        unreadCount: chat.unreadCount || 0,
        isGroup: chat.isGroup || false,
        contact: chat.contact ? {
          name: chat.contact.name || chat.contact.pushname,
          phone: chat.contact.number || chat.contact.id?.user
        } : null,
        timestamp: chat.timestamp || Date.now()
      }));

      // Ordenar por timestamp da última mensagem (mais recente primeiro)
      formattedChats.sort((a, b) => {
        const timestampA = a.lastMessage?.timestamp || a.timestamp || 0;
        const timestampB = b.lastMessage?.timestamp || b.timestamp || 0;
        return timestampB - timestampA;
      });

      this.logger.info(`📱 ${formattedChats.length} conversas obtidas com sucesso`);
      return formattedChats;

    } catch (error) {
      this.logger.error('❌ Erro ao obter conversas:', error.message);
      throw new Error('Falha ao obter conversas do WhatsApp');
    }
  }

  async getChatMessages(chatId, limit = 100) {
    try {
      if (!this.isConnected) {
        throw new Error('WhatsApp não está conectado');
      }

      const response = await this.httpClient.get(`/api/${this.sessionName}/all-messages-in-chat/${encodeURIComponent(chatId)}`, {
        params: { count: limit }
      });

      return response.data;

    } catch (error) {
      this.logger.error('❌ Erro ao obter mensagens do chat:', error);
      throw error;
    }
  }

  // Método para sincronizar histórico de um chat
  async syncChatHistory(chatId, limit = 200) {
    try {
      if (!this.isConnected) {
        throw new Error('WhatsApp não está conectado');
      }

      this.logger.info(`🔄 Sincronizando histórico do chat ${chatId} (limite: ${limit})`);

      // Obter mensagens do chat
      const messages = await this.getChatMessages(chatId, limit);

      // Aqui você pode implementar a lógica de persistência das mensagens
      // Por exemplo, salvar em banco de dados, cache, etc.

      this.logger.info(`✅ ${messages.length} mensagens sincronizadas para o chat ${chatId}`);

      return {
        success: true,
        chatId,
        messagesCount: messages.length,
        messages: messages
      };

    } catch (error) {
      this.logger.error(`❌ Erro ao sincronizar histórico do chat ${chatId}:`, error.message);
      return {
        success: false,
        chatId,
        error: error.message
      };
    }
  }

  // Método para sincronizar todas as conversas com mensagens usando getAllChatsWithMessages
  async syncAllChatsWithMessages(newOnly = false) {
    try {
      if (!this.isConnected) {
        throw new Error('WhatsApp não está conectado');
      }

      // Definir status de sincronização
      this.isSyncing = true;
      this.syncProgress = { step: 'Iniciando sincronização...', progress: 0 };

      this.logger.info(`🔄 Sincronizando ${newOnly ? 'novas' : 'todas as'} conversas com mensagens...`);

      // Atualizar progresso
      this.syncProgress = { step: 'Obtendo conversas do WhatsApp...', progress: 25 };

      // Primeiro, obter todos os chats
      const chatsResponse = await this.httpClient.get(`/api/${this.sessionName}/all-chats`);
      const allChats = chatsResponse.data || [];

      // Atualizar progresso
      this.syncProgress = { step: 'Obtendo mensagens das conversas...', progress: 50 };

      // Para cada chat, obter as mensagens se necessário
      const chatsData = [];
      for (let i = 0; i < allChats.length; i++) {
        const chat = allChats[i];

        try {
          // Se newOnly for true, pular chats sem mensagens não lidas
          if (newOnly && (!chat.unreadCount || chat.unreadCount === 0)) {
            continue;
          }

          // Obter mensagens do chat (limitado a 20 mensagens por chat para performance)
          const messagesResponse = await this.httpClient.get(
            `/api/${this.sessionName}/all-messages-in-chat/${encodeURIComponent(chat.id._serialized || chat.id)}`,
            { params: { count: 20 } }
          );

          chat.messages = messagesResponse.data || [];
          chatsData.push(chat);

          // Atualizar progresso
          const progress = 50 + Math.floor((i / allChats.length) * 40);
          this.syncProgress = { step: `Processando chat ${i + 1}/${allChats.length}...`, progress };

          // Pequena pausa para não sobrecarregar a API
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (chatError) {
          this.logger.warn(`⚠️ Erro ao obter mensagens do chat ${chat.id}:`, chatError.message);
          // Adicionar chat sem mensagens
          chat.messages = [];
          chatsData.push(chat);
        }
      }
      // Processar e formatar os dados das conversas
      this.syncProgress = { step: 'Formatando dados...', progress: 90 };

      const formattedChats = chatsData.map(chat => ({
        id: chat.id._serialized || chat.id,
        name: chat.name || chat.contact?.name || chat.contact?.pushname || 'Contato sem nome',
        isGroup: chat.isGroup || false,
        unreadCount: chat.unreadCount || 0,
        lastMessage: chat.lastMessage ? {
          id: chat.lastMessage.id,
          body: chat.lastMessage.body || '',
          timestamp: chat.lastMessage.timestamp || Date.now(),
          fromMe: chat.lastMessage.fromMe || false,
          type: chat.lastMessage.type || 'text',
          author: chat.lastMessage.author
        } : null,
        messages: (chat.messages || []).slice(0, 20).map(msg => ({
          id: msg.id || msg._serialized,
          body: msg.body || msg.content || '',
          timestamp: msg.timestamp || msg.t || Date.now(),
          fromMe: msg.fromMe || false,
          type: msg.type || 'text',
          author: msg.author || msg.from,
          quotedMsg: msg.quotedMsg
        })),
        contact: chat.contact ? {
          id: chat.contact.id,
          name: chat.contact.name || chat.contact.pushname,
          phone: chat.contact.number || chat.contact.id?.user,
          profilePicUrl: chat.contact.profilePicUrl
        } : null,
        timestamp: chat.timestamp || Date.now()
      }));

      // Atualizar progresso
      this.syncProgress = { step: 'Finalizando sincronização...', progress: 90 };

      // Ordenar por timestamp da última mensagem (mais recente primeiro)
      formattedChats.sort((a, b) => {
        const timestampA = a.lastMessage?.timestamp || a.timestamp || 0;
        const timestampB = b.lastMessage?.timestamp || b.timestamp || 0;
        return timestampB - timestampA;
      });

      this.logger.info(`✅ ${formattedChats.length} conversas sincronizadas com sucesso`);

      // Finalizar sincronização
      this.isSyncing = false;
      this.syncProgress = null;

      return {
        success: true,
        chatsCount: formattedChats.length,
        chats: formattedChats,
        newOnly: newOnly,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('❌ Erro ao sincronizar conversas com mensagens:', error.message);

      // Finalizar sincronização em caso de erro
      this.isSyncing = false;
      this.syncProgress = null;

      // Fallback: tentar usar o método tradicional
      try {
        this.logger.info('🔄 Tentando fallback com método tradicional...');
        const fallbackChats = await this.getSyncedChats();

        return {
          success: true,
          chatsCount: fallbackChats.length,
          chats: fallbackChats,
          newOnly: false,
          fallback: true,
          timestamp: new Date().toISOString()
        };

      } catch (fallbackError) {
        this.logger.error('❌ Fallback também falhou:', fallbackError.message);
        throw new Error('Falha ao sincronizar conversas');
      }
    }
  }

  // Método para processar webhook do WPPConnect Server
  async processWebhook(webhookData) {
    try {
      const { event, session, data } = webhookData;

      this.logger.debug(`📨 Webhook recebido: ${event} para sessão ${session}`);

      // Verificar e sincronizar status de conexão se necessário
      if (event === 'message' && !this.isConnected) {
        const serverStatus = await this.getSessionStatus();
        if (serverStatus && serverStatus.connected) {
          this.logger.info('✅ Sincronizando status para processamento de mensagem');
          this.isConnected = true;
        }
      }

      switch (event) {
        case 'qrcode':
          await this.handleQRCodeWebhook(data);
          break;
          
        case 'connection':
          await this.handleConnectionWebhook(data);
          break;
          
        case 'message':
          await this.handleMessageWebhook(data);
          break;
          
        case 'disconnected':
          await this.handleDisconnectedWebhook(data);
          break;
          
        default:
          this.logger.debug(`📨 Evento webhook não tratado: ${event}`);
      }
      
    } catch (error) {
      this.logger.error('❌ Erro ao processar webhook:', error);
    }
  }

  async handleQRCodeWebhook(data) {
    this.qrCode = {
      base64: data.qrcode,
      timestamp: new Date().toISOString()
    };
    
    this.emit('qr', this.qrCode.base64);
    
    if (this.onQRCode) {
      await this.onQRCode(this.qrCode.base64, null);
    }
  }

  async handleConnectionWebhook(data) {
    if (data.state === 'CONNECTED') {
      this.isConnected = true;
      this.connectionAttempts = 0;
      this.logger.info('✅ WhatsApp conectado via webhook');
      this.emit('ready');
      
      if (this.onReady) {
        await this.onReady();
      }
    }
  }

  async handleMessageWebhook(data) {
    try {
      this.logger.debug('📨 Nova mensagem via webhook');
      await this.handleIncomingMessage(data);
    } catch (error) {
      this.logger.error('❌ Erro ao processar mensagem do webhook:', error);
    }
  }

  async handleDisconnectedWebhook(data) {
    this.isConnected = false;
    this.logger.warn('⚠️ WhatsApp desconectado via webhook');
    this.emit('disconnected');

    if (this.onDisconnected) {
      await this.onDisconnected();
    }

    // Tentar reconexão
    this.scheduleReconnect();
  }

  async handleIncomingMessage(message) {
    try {
      this.logger.debug('📨 Processando mensagem recebida');

      // Ignorar mensagens de status
      if (message.isStatus || message.from?.includes('status@broadcast')) {
        this.logger.debug('📨 Ignorando mensagem de status');
        return;
      }

      // Ignorar mensagens próprias
      if (message.fromMe) {
        this.logger.debug('📨 Ignorando mensagem própria');
        return;
      }

      // Emitir evento de mensagem
      this.emit('message', message);

      if (this.onMessage) {
        await this.onMessage(message);
      }

    } catch (error) {
      this.logger.error('❌ Erro ao processar mensagem recebida:', error);
    }
  }

  async loadPersistedSession() {
    try {
      const sessionData = await this.persistenceService.loadSession(this.sessionName);
      if (sessionData) {
        this.logger.info('✅ Sessão persistida carregada');
        return sessionData;
      }
    } catch (error) {
      this.logger.warn('⚠️ Erro ao carregar sessão persistida:', error.message);
    }
    return null;
  }

  async saveSession() {
    try {
      const sessionData = {
        sessionName: this.sessionName,
        isConnected: this.isConnected,
        timestamp: new Date().toISOString()
      };

      await this.persistenceService.saveSession(this.sessionName, sessionData);
      this.logger.info('✅ Sessão salva com sucesso');
    } catch (error) {
      this.logger.error('❌ Erro ao salvar sessão:', error);
    }
  }

  setupHealthMonitoring() {
    this.logger.info('🏥 Configurando monitoramento de saúde...');

    this.healthCheckInterval = setInterval(async () => {
      try {
        const status = await this.getSessionStatus();

        // Verificar se está conectado baseado no status
        const isServerConnected = status && (
          status.status === 'isLogged' ||
          status.status === 'CONNECTED' ||
          status.connected === true
        );

        if (isServerConnected && !this.isConnected) {
          this.logger.info('✅ Conexão restaurada detectada');
          this.isConnected = true;
          this.emit('ready');
        } else if (!isServerConnected && this.isConnected) {
          this.logger.warn('⚠️ Possível desconexão detectada (ignorando por enquanto)');
          // Temporariamente desabilitado para evitar falsas desconexões
          // this.isConnected = false;
          // this.emit('disconnected');
          // this.scheduleReconnect();
        }

      } catch (error) {
        this.logger.warn('⚠️ Erro no health check:', error.message);
      }
    }, this.healthCheckDelay);
  }

  scheduleReconnect() {
    if (this.reconnectInterval) {
      clearTimeout(this.reconnectInterval);
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.logger.error('❌ Máximo de tentativas de reconexão atingido');
      return;
    }

    const delay = Math.min(
      this.reconnectDelay * Math.pow(2, this.reconnectAttempts),
      this.maxReconnectDelay
    );

    this.logger.info(`🔄 Agendando reconexão em ${delay}ms (tentativa ${this.reconnectAttempts + 1})`);

    this.reconnectInterval = setTimeout(async () => {
      try {
        this.reconnectAttempts++;
        await this.initializeSession();
      } catch (error) {
        this.logger.error('❌ Erro na reconexão:', error);
        this.scheduleReconnect();
      }
    }, delay);
  }

  setupAutoBackup() {
    this.logger.info('💾 Configurando backup automático...');

    cron.schedule(this.backupInterval, async () => {
      try {
        await this.createBackup();
      } catch (error) {
        this.logger.error('❌ Erro no backup automático:', error);
      }
    });
  }

  async createBackup() {
    try {
      this.logger.info('💾 Criando backup...');

      const backupData = {
        sessionName: this.sessionName,
        isConnected: this.isConnected,
        timestamp: new Date().toISOString(),
        chats: await this.getChats().catch(() => [])
      };

      await this.persistenceService.createBackup(this.sessionName, backupData);
      this.logger.info('✅ Backup criado com sucesso');

    } catch (error) {
      this.logger.error('❌ Erro ao criar backup:', error);
    }
  }

  async restart() {
    try {
      this.logger.info('🔄 Reiniciando sessão WhatsApp...');

      // Parar sessão atual
      await this.stop();

      // Aguardar um pouco
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Reinicializar
      await this.initialize();

      this.logger.info('✅ Sessão reiniciada com sucesso');
      return true;

    } catch (error) {
      this.logger.error('❌ Erro ao reiniciar sessão:', error);
      throw error;
    }
  }

  async stop() {
    try {
      this.logger.info('🛑 Parando sessão WhatsApp...');

      // Limpar intervalos
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      if (this.reconnectInterval) {
        clearTimeout(this.reconnectInterval);
        this.reconnectInterval = null;
      }

      // Parar sessão no servidor
      await this.httpClient.post(`/api/${this.sessionName}/close-session`);

      this.isConnected = false;
      this.emit('disconnected');

      this.logger.info('✅ Sessão parada com sucesso');

    } catch (error) {
      this.logger.error('❌ Erro ao parar sessão:', error);
      throw error;
    }
  }

  // Métodos de compatibilidade com o WhatsAppService original
  async sendImage(to, imagePath, caption = '', options = {}) {
    try {
      if (!this.isConnected) {
        throw new Error('WhatsApp não está conectado');
      }

      const response = await this.httpClient.post(`/api/${this.sessionName}/send-image`, {
        phone: to,
        path: imagePath,
        caption: caption,
        ...options
      });

      this.logger.info(`📤 Imagem enviada para ${to}`);
      return response.data;

    } catch (error) {
      this.logger.error('❌ Erro ao enviar imagem:', error);
      throw error;
    }
  }

  async sendDocument(to, documentPath, filename, caption = '', options = {}) {
    try {
      if (!this.isConnected) {
        throw new Error('WhatsApp não está conectado');
      }

      const response = await this.httpClient.post(`/api/${this.sessionName}/send-file`, {
        phone: to,
        path: documentPath,
        filename: filename,
        caption: caption,
        ...options
      });

      this.logger.info(`📤 Documento enviado para ${to}: ${filename}`);
      return response.data;

    } catch (error) {
      this.logger.error('❌ Erro ao enviar documento:', error);
      throw error;
    }
  }

  // Getters para compatibilidade
  get client() {
    return {
      // Proxy para métodos que podem ser chamados diretamente
      sendMessage: this.sendMessage.bind(this),
      sendImage: this.sendImage.bind(this),
      sendDocument: this.sendDocument.bind(this),
      getChats: this.getChats.bind(this),
      getChatMessages: this.getChatMessages.bind(this)
    };
  }

  // Processar webhook do WPPConnect Server
  async processWebhook(webhookData) {
    try {
      const { session, message, timestamp } = webhookData;

      this.logger.info(`📡 Processando webhook da sessão ${session}`);

      // Verificar se é uma mensagem
      if (message) {
        // Verificar se a mensagem não é nossa (evitar loop)
        if (message.isFromMe) {
          this.logger.info('📤 Mensagem ignorada (enviada por nós)');
          return;
        }

        // Verificar se é uma mensagem de status (ignorar)
        if (message.from && message.from.includes('status@broadcast')) {
          this.logger.info('📢 Status ignorado');
          return;
        }

        // Processar mensagem através do callback
        if (this.onMessage) {
          const formattedMessage = {
            id: message.id,
            from: message.from,
            body: message.body,
            timestamp: message.timestamp,
            isFromMe: message.isFromMe,
            notifyName: message.notifyName,
            type: message.type || 'chat'
          };

          await this.onMessage(formattedMessage);
          this.logger.info('✅ Mensagem processada via webhook');
        }
      }

    } catch (error) {
      this.logger.error('❌ Erro ao processar webhook:', error.message);
      throw error;
    }
  }

  // Método para obter status da conexão
  async getConnectionStatus() {
    try {
      // Tentar obter status do WPPConnect Server
      let serverStatus = null;
      try {
        const response = await this.httpClient.get(`/api/${this.sessionName}/status`);
        serverStatus = response.data;
      } catch (error) {
        this.logger.warn('⚠️ Não foi possível obter status do servidor:', error.message);
      }

      let status = 'Desconectado';
      let statusCode = 'disconnected';

      if (this.isConnected || (serverStatus && serverStatus.status === 'CONNECTED')) {
        status = 'Conectado';
        statusCode = 'connected';
      } else if (this.qrCode || (serverStatus && serverStatus.qrCode)) {
        status = 'Aguardando QR Code';
        statusCode = 'waiting_qr';

        // Atualizar QR Code se disponível no servidor
        if (serverStatus && serverStatus.qrCode && !this.qrCode) {
          this.qrCode = {
            base64: serverStatus.qrCode.base64,
            timestamp: serverStatus.qrCode.timestamp || new Date().toISOString()
          };
        }
      } else if (this.connectionAttempts > 0) {
        status = 'Conectando...';
        statusCode = 'connecting';
      }

      return {
        isConnected: this.isConnected || (serverStatus && serverStatus.status === 'CONNECTED'),
        sessionName: this.sessionName,
        qrCode: this.qrCode,
        hasQRCode: !!(this.qrCode || (serverStatus && serverStatus.qrCode)),
        qrCodeUrl: this.qrCode ? this.qrCode.base64 : (serverStatus && serverStatus.qrCode ? serverStatus.qrCode.base64 : null),
        connectionAttempts: this.connectionAttempts,
        maxAttempts: this.maxConnectionAttempts,
        reconnectAttempts: this.reconnectAttempts,
        status,
        statusCode,
        timestamp: new Date().toISOString(),
        simulationMode: false,
        serviceType: 'http',
        serverUrl: this.serverUrl,
        serverStatus: serverStatus
      };
    } catch (error) {
      this.logger.error('❌ Erro ao obter status da conexão:', error.message);

      // Retornar status básico em caso de erro
      return {
        isConnected: false,
        sessionName: this.sessionName,
        qrCode: null,
        hasQRCode: false,
        qrCodeUrl: null,
        connectionAttempts: this.connectionAttempts,
        maxAttempts: this.maxConnectionAttempts,
        reconnectAttempts: this.reconnectAttempts,
        status: 'Erro de Conexão',
        statusCode: 'error',
        timestamp: new Date().toISOString(),
        simulationMode: false,
        serviceType: 'http',
        serverUrl: this.serverUrl,
        error: error.message
      };
    }
  }

  // Método para obter estatísticas
  getStats() {
    return {
      isConnected: this.isConnected,
      sessionName: this.sessionName,
      connectionAttempts: this.connectionAttempts,
      reconnectAttempts: this.reconnectAttempts,
      hasQRCode: !!this.qrCode,
      serverUrl: this.serverUrl
    };
  }

  // Métodos para compatibilidade com MessageHandler
  setOnMessage(callback) {
    this.onMessage = callback;
    this.logger.debug('📨 Callback onMessage configurado');
  }

  setOnReady(callback) {
    this.onReady = callback;
    this.logger.debug('✅ Callback onReady configurado');
  }

  setOnDisconnected(callback) {
    this.onDisconnected = callback;
    this.logger.debug('❌ Callback onDisconnected configurado');
  }

  setOnQRCode(callback) {
    this.onQRCode = callback;
    this.logger.debug('📱 Callback onQRCode configurado');
  }

  setOnStatusChange(callback) {
    this.onStatusChange = callback;
    this.logger.debug('📊 Callback onStatusChange configurado');
  }
}
