{"name": "vereadora-rafaela-whatsapp-backend", "version": "1.0.0", "description": "Backend WhatsApp para Assistente Virtual da Vereadora Rafaela de Nilda", "main": "src/server.js", "type": "module", "scripts": {"start": "node start.js start", "stop": "node start.js stop", "restart": "node start.js restart", "status": "node start.js status", "logs": "node start.js logs", "dev": "node --watch start.js", "dev:direct": "node src/server.js", "start:simulator": "WHATSAPP_USE_SIMULATOR=true node src/server.js", "start:real": "WHATSAPP_USE_REAL=true node src/server.js", "dev:node": "node src/server.js", "dev:full": "npm run check:wppconnect && npm run dev", "pm2:start": "pm2 start ecosystem.config.cjs", "pm2:stop": "pm2 stop vereadora-rafaela", "pm2:restart": "pm2 restart vereadora-raf<PERSON>a", "pm2:reload": "pm2 reload vereadora-rafaela", "pm2:delete": "pm2 delete vereadora-rafaela", "pm2:logs": "pm2 logs vereadora-rafaela", "pm2:monit": "pm2 monit", "pm2:status": "pm2 status", "pm2:dev": "pm2 start ecosystem.config.cjs --env development", "pm2:prod": "pm2 start ecosystem.config.cjs --env production", "build": "echo 'No build step required'", "test": "echo 'No tests specified'", "check:wppconnect": "node scripts/check-wppconnect.js check", "status:wppconnect": "node scripts/check-wppconnect.js status", "start:wppconnect": "node scripts/check-wppconnect.js start", "wppconnect:server": "node start-wppconnect.js", "wppconnect:dev": "node --watch start-wppconnect.js", "start:full": "node start-full-system.js", "start:full:concurrent": "concurrently \"npm run wppconnect:server\" \"npm run start\"", "verify": "node scripts/verify-system.js", "verify:system": "node scripts/verify-system.js"}, "keywords": ["whatsapp", "wppconnect", "chatbot", "vereadora", "parnam<PERSON>m", "rag", "ai"], "author": "Sistema RAG Vereadora Rafaela de Nilda", "license": "MIT", "dependencies": {"axios": "^1.6.2", "compression": "^1.7.4", "concurrently": "^9.2.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "morgan": "^1.10.0", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "qrcode": "^1.5.3", "winston": "^3.11.0"}, "devDependencies": {"pm2": "^5.3.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}