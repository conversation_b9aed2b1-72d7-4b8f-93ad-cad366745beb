import express from 'express';
import { Logger } from '../utils/Logger.js';

const router = express.Router();
const logger = new Logger();

// Middleware para verificar se o WhatsApp está disponível
const checkWhatsAppService = (req, res, next) => {
  if (!req.app.locals.whatsappService) {
    return res.status(503).json({
      error: 'WhatsApp service não disponível',
      message: 'O serviço WhatsApp não foi inicializado',
      code: 'SERVICE_UNAVAILABLE'
    });
  }
  next();
};

// GET /api/whatsapp/status - Status da conexão WhatsApp
router.get('/status', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;

    // Verificar se o método é assíncrono
    let status;
    if (typeof whatsappService.getConnectionStatus === 'function') {
      const result = whatsappService.getConnectionStatus();
      // Se retorna uma Promise, aguardar
      if (result && typeof result.then === 'function') {
        status = await result;
      } else {
        status = result;
      }
    } else {
      throw new Error('Método getConnectionStatus não disponível');
    }

    // Adicionar informação sobre o tipo de serviço
    if (!status.serviceType) {
      status.serviceType = whatsappService.simulationMode ? 'simulator' : 'http';
    }
    status.isSimulator = !!whatsappService.simulationMode;

    // Adicionar informações de sincronização
    status.isSyncing = whatsappService.isSyncing || false;
    status.syncProgress = whatsappService.syncProgress || null;

    res.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao obter status WhatsApp:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível obter o status do WhatsApp',
      code: 'INTERNAL_ERROR'
    });
  }
});

// GET /api/whatsapp/qr - Obter QR Code atual
router.get('/qr', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;

    // Verificar se o método é assíncrono
    let status;
    if (typeof whatsappService.getConnectionStatus === 'function') {
      const result = whatsappService.getConnectionStatus();
      // Se retorna uma Promise, aguardar
      if (result && typeof result.then === 'function') {
        status = await result;
      } else {
        status = result;
      }
    } else {
      throw new Error('Método getConnectionStatus não disponível');
    }

    if (!status.qrCode) {
      // Tentar obter QR code diretamente do WPPConnect Server como fallback
      try {
        const axios = require('axios');
        const wppResponse = await axios.get('http://localhost:21465/api/vereadora-rafaela/qr-code', {
          timeout: 3000
        });

        if (wppResponse.data && (wppResponse.data.qrCode || wppResponse.data.qr)) {
          const qrCodeData = wppResponse.data.qrCode || wppResponse.data.qr;
          return res.json({
            success: true,
            data: {
              qrCode: qrCodeData,
              isConnected: false,
              sessionName: 'vereadora-rafaela',
              source: 'wppconnect-server'
            },
            timestamp: new Date().toISOString()
          });
        }
      } catch (wppError) {
        logger.debug('Fallback WPPConnect Server falhou:', wppError.message);
      }

      return res.status(404).json({
        error: 'QR Code não disponível',
        message: 'Nenhum QR Code foi gerado ou o WhatsApp já está conectado',
        code: 'QR_NOT_AVAILABLE'
      });
    }

    res.json({
      success: true,
      data: {
        qrCode: status.qrCode,
        isConnected: status.isConnected,
        sessionName: status.sessionName
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao obter QR Code:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: 'Não foi possível obter o QR Code',
      code: 'INTERNAL_ERROR'
    });
  }
});

// POST /api/whatsapp/send - Enviar mensagem
router.post('/send', checkWhatsAppService, async (req, res) => {
  try {
    const { to, message, type = 'text' } = req.body;
    
    // Validação
    if (!to || !message) {
      return res.status(400).json({
        error: 'Dados inválidos',
        message: 'Campos "to" e "message" são obrigatórios',
        code: 'VALIDATION_ERROR'
      });
    }
    
    const whatsappService = req.app.locals.whatsappService;
    
    // Verificar se está conectado
    if (!whatsappService.isConnected) {
      return res.status(503).json({
        error: 'WhatsApp não conectado',
        message: 'O WhatsApp precisa estar conectado para enviar mensagens',
        code: 'NOT_CONNECTED'
      });
    }
    
    let result;
    
    switch (type) {
      case 'text':
        result = await whatsappService.sendMessage(to, message);
        break;
      case 'image':
        const { imagePath, caption } = req.body;
        result = await whatsappService.sendImage(to, imagePath, caption);
        break;
      case 'document':
        const { documentPath, filename, caption: docCaption } = req.body;
        result = await whatsappService.sendDocument(to, documentPath, filename, docCaption);
        break;
      default:
        return res.status(400).json({
          error: 'Tipo inválido',
          message: 'Tipo de mensagem não suportado',
          code: 'INVALID_TYPE'
        });
    }
    
    logger.whatsappMessage('sent', 'system', to, message);
    
    res.json({
      success: true,
      data: {
        messageId: result.id,
        to,
        type,
        status: 'sent',
        timestamp: new Date().toISOString()
      },
      message: 'Mensagem enviada com sucesso'
    });
    
  } catch (error) {
    logger.error('Erro ao enviar mensagem WhatsApp:', error);
    res.status(500).json({
      error: 'Erro ao enviar mensagem',
      message: error.message,
      code: 'SEND_ERROR'
    });
  }
});



// GET /api/whatsapp/contacts - Obter lista de contatos
router.get('/contacts', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;

    if (!whatsappService.isConnected) {
      return res.status(503).json({
        error: 'WhatsApp não conectado',
        message: 'O WhatsApp precisa estar conectado para obter contatos',
        code: 'NOT_CONNECTED'
      });
    }

    const contacts = await whatsappService.getContacts();

    // Filtrar e formatar dados sensíveis
    const formattedContacts = contacts.map(contact => ({
      id: contact.id,
      name: contact.name || contact.pushname,
      isMyContact: contact.isMyContact,
      isUser: contact.isUser,
      isWAContact: contact.isWAContact
    }));

    res.json({
      success: true,
      data: {
        contacts: formattedContacts,
        total: formattedContacts.length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao obter contatos WhatsApp:', error);
    res.status(500).json({
      error: 'Erro ao obter contatos',
      message: error.message,
      code: 'CONTACTS_ERROR'
    });
  }
});

// GET /api/whatsapp/contacts/:contactId - Obter informações de um contato específico
router.get('/contacts/:contactId', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;
    const { contactId } = req.params;

    if (!whatsappService.isConnected) {
      return res.status(503).json({
        error: 'WhatsApp não conectado',
        message: 'O WhatsApp precisa estar conectado para obter informações do contato',
        code: 'NOT_CONNECTED'
      });
    }

    const contact = await whatsappService.getContact(contactId);

    if (!contact) {
      return res.status(404).json({
        error: 'Contato não encontrado',
        message: 'Não foi possível encontrar informações para este contato',
        code: 'CONTACT_NOT_FOUND'
      });
    }

    // Formatar dados do contato
    const formattedContact = {
      id: contact.id,
      name: contact.name || contact.pushname || 'Sem nome',
      pushname: contact.pushname,
      isMyContact: contact.isMyContact,
      isUser: contact.isUser,
      isWAContact: contact.isWAContact,
      profilePicThumbObj: contact.profilePicThumbObj || null
    };

    res.json({
      success: true,
      data: formattedContact,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao obter contato:', error);
    res.status(500).json({
      error: 'Erro ao obter contato',
      message: 'Não foi possível carregar as informações do contato',
      code: 'CONTACT_ERROR'
    });
  }
});

// POST /api/whatsapp/restart - Reiniciar conexão WhatsApp
router.post('/restart', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;
    
    logger.info('Reiniciando conexão WhatsApp...');
    
    // Parar serviço atual
    await whatsappService.stop();
    
    // Aguardar um pouco
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Reinicializar
    await whatsappService.initialize();
    
    res.json({
      success: true,
      message: 'WhatsApp reiniciado com sucesso',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Erro ao reiniciar WhatsApp:', error);
    res.status(500).json({
      error: 'Erro ao reiniciar',
      message: error.message,
      code: 'RESTART_ERROR'
    });
  }
});

// GET /api/whatsapp/stats - Estatísticas do WhatsApp
router.get('/stats', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;
    const messageHandler = req.app.locals.messageHandler;
    
    const whatsappStatus = whatsappService.getConnectionStatus();
    const messageStats = messageHandler ? messageHandler.getStats() : {};
    
    res.json({
      success: true,
      data: {
        connection: whatsappStatus,
        messages: messageStats,
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Erro ao obter estatísticas WhatsApp:', error);
    res.status(500).json({
      error: 'Erro ao obter estatísticas',
      message: error.message,
      code: 'STATS_ERROR'
    });
  }
});

// Middleware de tratamento de erros específico para WhatsApp
router.use((error, req, res, next) => {
  logger.error('Erro nas rotas WhatsApp:', error);
  
  res.status(500).json({
    error: 'Erro interno do WhatsApp',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Erro interno',
    code: 'WHATSAPP_ERROR',
    timestamp: new Date().toISOString()
  });
});

// ===== ROTAS DE CONVERSAS SINCRONIZADAS =====

// GET /api/whatsapp/chats - Obter conversas sincronizadas
router.get('/chats', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;
    const { newOnly = false } = req.query;

    // Verificar se o WhatsApp está conectado
    const status = whatsappService.getConnectionStatus();
    if (!status.isConnected && !whatsappService.isConnected) {
      return res.status(400).json({
        error: 'WhatsApp não conectado',
        message: 'É necessário conectar o WhatsApp escaneando o QR Code antes de obter conversas',
        code: 'NOT_CONNECTED',
        needsQR: true
      });
    }

    // Usar o novo método de sincronização para obter conversas
    const result = await whatsappService.syncAllChatsWithMessages(newOnly === 'true');

    if (result.success) {
      res.json({
        success: true,
        data: result.chats,
        total: result.chatsCount,
        newOnly: result.newOnly,
        fallback: result.fallback || false,
        timestamp: result.timestamp
      });
    } else {
      // Fallback para o método antigo se o novo falhar
      const chats = await whatsappService.getSyncedChats();
      res.json({
        success: true,
        data: chats,
        total: chats.length,
        fallback: true,
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    logger.error('Erro ao obter conversas:', error);
    res.status(500).json({
      error: 'Erro ao obter conversas',
      message: 'Não foi possível carregar as conversas sincronizadas',
      code: 'CHATS_ERROR'
    });
  }
});

// GET /api/whatsapp/chats/:chatId/messages - Obter mensagens de uma conversa
router.get('/chats/:chatId/messages', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;
    const { chatId } = req.params;
    const { limit = 100 } = req.query;

    const messages = await whatsappService.getChatMessages(chatId, parseInt(limit));

    res.json({
      success: true,
      data: messages,
      total: messages.length,
      chatId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao obter mensagens:', error);
    res.status(500).json({
      error: 'Erro ao obter mensagens',
      message: 'Não foi possível carregar as mensagens da conversa',
      code: 'MESSAGES_ERROR'
    });
  }
});

// POST /api/whatsapp/chats/:chatId/sync-history - Sincronizar histórico de um chat
router.post('/chats/:chatId/sync-history', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;
    const { chatId } = req.params;
    const { limit = 200 } = req.body;

    logger.info(`🔄 Iniciando sincronização de histórico para chat ${chatId}`);

    const result = await whatsappService.syncChatHistory(chatId, parseInt(limit));

    if (result.success) {
      res.json({
        success: true,
        message: 'Histórico sincronizado com sucesso',
        data: {
          chatId: result.chatId,
          messagesCount: result.messagesCount
        },
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Erro na sincronização',
        message: result.error,
        code: 'SYNC_HISTORY_ERROR'
      });
    }

  } catch (error) {
    logger.error('Erro ao sincronizar histórico:', error);
    res.status(500).json({
      error: 'Erro ao sincronizar histórico',
      message: 'Não foi possível sincronizar o histórico da conversa',
      code: 'SYNC_HISTORY_ERROR'
    });
  }
});

// POST /api/whatsapp/sync - Iniciar sincronização manual
router.post('/sync', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;

    // Verificar se o WhatsApp está conectado
    const status = whatsappService.getConnectionStatus();
    if (!status.isConnected && !whatsappService.isConnected) {
      return res.status(400).json({
        error: 'WhatsApp não conectado',
        message: 'É necessário conectar o WhatsApp escaneando o QR Code antes de sincronizar',
        code: 'NOT_CONNECTED',
        needsQR: true
      });
    }

    const { newOnly = false } = req.body;

    logger.info(`🔄 Iniciando sincronização de ${newOnly ? 'novas' : 'todas as'} conversas`);

    // Usar o novo método de sincronização com getAllChatsWithMessages
    const result = await whatsappService.syncAllChatsWithMessages(newOnly);

    if (result.success) {
      res.json({
        success: true,
        message: `${result.chatsCount} conversas sincronizadas com sucesso`,
        data: {
          chatsCount: result.chatsCount,
          chats: result.chats,
          newOnly: result.newOnly,
          fallback: result.fallback || false
        },
        timestamp: result.timestamp
      });
    } else {
      res.status(500).json({
        error: 'Erro na sincronização',
        message: 'Não foi possível sincronizar as conversas',
        code: 'SYNC_FAILED'
      });
    }

  } catch (error) {
    logger.error('Erro ao iniciar sincronização:', error);
    res.status(500).json({
      error: 'Erro na sincronização',
      message: 'Não foi possível iniciar a sincronização',
      code: 'SYNC_ERROR'
    });
  }
});

// GET /api/whatsapp/stats - Estatísticas das conversas
router.get('/stats', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;
    const sessionManager = req.app.locals.sessionManager;

    const stats = await sessionManager.getConversationStats(whatsappService.sessionName);

    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao obter estatísticas:', error);
    res.status(500).json({
      error: 'Erro nas estatísticas',
      message: 'Não foi possível obter as estatísticas',
      code: 'STATS_ERROR'
    });
  }
});

// POST /api/whatsapp/test-rafaela - Testar respostas da Vereadora Rafaela
router.post('/test-rafaela', async (req, res) => {
  try {
    const { message, contact } = req.body;

    if (!message) {
      return res.status(400).json({
        error: 'Mensagem obrigatória',
        message: 'É necessário fornecer uma mensagem para testar',
        code: 'MISSING_MESSAGE'
      });
    }

    // Importar o serviço de respostas da Rafaela
    const { default: RafaelaResponseService } = await import('../services/RafaelaResponseService.js');
    const rafaelaService = new RafaelaResponseService();

    // Gerar resposta
    const shouldRespond = rafaelaService.shouldAutoRespond(message, contact);
    const response = rafaelaService.generateResponse(message, contact);
    const personalizedResponse = rafaelaService.addPersonalizedContext(response, contact);

    res.json({
      success: true,
      data: {
        originalMessage: message,
        contact: contact || null,
        shouldAutoRespond: shouldRespond,
        response: response,
        personalizedResponse: personalizedResponse,
        stats: rafaelaService.getStats()
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao testar respostas da Rafaela:', error);
    res.status(500).json({
      error: 'Erro no teste',
      message: 'Não foi possível testar o sistema de respostas',
      code: 'TEST_ERROR'
    });
  }
});

// POST /api/whatsapp/test-message-handler - Testar recebimento de mensagens
router.post('/test-message-handler', async (req, res) => {
  try {
    const { message, from, name } = req.body;

    if (!message || !from) {
      return res.status(400).json({
        error: 'Dados obrigatórios',
        message: 'É necessário fornecer message e from',
        code: 'REQUIRED_FIELDS'
      });
    }

    const messageHandler = req.app.locals.messageHandler;

    // Simular mensagem recebida
    const mockMessage = {
      from: from,
      body: message,
      notifyName: name || 'Teste',
      pushname: name || 'Teste',
      fromMe: false,
      isGroupMsg: false,
      type: 'text',
      timestamp: Date.now(),
      hasMedia: false
    };

    logger.info('🧪 Testando handler de mensagem com dados simulados:', mockMessage);

    // Testar diretamente o MessageHandler (sem precisar do WhatsApp conectado)
    if (messageHandler) {
      logger.info('✅ MessageHandler disponível, executando teste...');

      try {
        await messageHandler.handleMessage(mockMessage);

        res.json({
          success: true,
          message: 'Teste de handler executado com sucesso',
          data: {
            mockMessage,
            handlerAvailable: true,
            testCompleted: true
          },
          timestamp: new Date().toISOString()
        });
      } catch (handlerError) {
        logger.error('❌ Erro no handler:', handlerError);

        res.json({
          success: false,
          message: 'Erro ao executar handler',
          error: handlerError.message,
          data: {
            mockMessage,
            handlerAvailable: true,
            testCompleted: false
          },
          timestamp: new Date().toISOString()
        });
      }
    } else {
      logger.warn('⚠️ MessageHandler NÃO está disponível!');

      res.json({
        success: false,
        message: 'MessageHandler não está disponível',
        data: {
          mockMessage,
          handlerAvailable: false,
          testCompleted: false
        },
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    logger.error('Erro ao testar handler de mensagem:', error);
    res.status(500).json({
      error: 'Erro no teste',
      message: error.message,
      code: 'TEST_HANDLER_ERROR'
    });
  }
});

// POST /api/whatsapp/simulator/connect - Forçar conexão do simulador
router.post('/simulator/connect', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;

    if (!whatsappService.simulationMode) {
      return res.status(400).json({
        error: 'Não é um simulador',
        message: 'Esta ação só está disponível no modo simulador',
        code: 'NOT_SIMULATOR'
      });
    }

    await whatsappService.simulateConnection();

    res.json({
      success: true,
      message: 'Conexão simulada iniciada',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao conectar simulador:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: error.message,
      code: 'SIMULATOR_CONNECT_ERROR'
    });
  }
});

// POST /api/whatsapp/simulator/disconnect - Simular desconexão
router.post('/simulator/disconnect', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;

    if (!whatsappService.simulationMode) {
      return res.status(400).json({
        error: 'Não é um simulador',
        message: 'Esta ação só está disponível no modo simulador',
        code: 'NOT_SIMULATOR'
      });
    }

    whatsappService.simulateDisconnection();

    res.json({
      success: true,
      message: 'Desconexão simulada',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao desconectar simulador:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: error.message,
      code: 'SIMULATOR_DISCONNECT_ERROR'
    });
  }
});

// POST /api/whatsapp/simulator/message - Simular recebimento de mensagem
router.post('/simulator/message', checkWhatsAppService, async (req, res) => {
  try {
    const whatsappService = req.app.locals.whatsappService;
    const { from, message, name } = req.body;

    if (!whatsappService.simulationMode) {
      return res.status(400).json({
        error: 'Não é um simulador',
        message: 'Esta ação só está disponível no modo simulador',
        code: 'NOT_SIMULATOR'
      });
    }

    if (!from || !message) {
      return res.status(400).json({
        error: 'Dados inválidos',
        message: 'from e message são obrigatórios',
        code: 'INVALID_DATA'
      });
    }

    const simulatedMessage = {
      id: `sim_manual_${Date.now()}`,
      from: from,
      body: message,
      timestamp: Date.now(),
      isFromMe: false,
      notifyName: name || 'Usuário Teste',
      type: 'chat'
    };

    // Enviar mensagem para o handler se disponível
    if (whatsappService.onMessage) {
      whatsappService.onMessage(simulatedMessage);
    }

    res.json({
      success: true,
      message: 'Mensagem simulada enviada',
      data: simulatedMessage,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erro ao simular mensagem:', error);
    res.status(500).json({
      error: 'Erro interno',
      message: error.message,
      code: 'SIMULATOR_MESSAGE_ERROR'
    });
  }
});

export default router;
