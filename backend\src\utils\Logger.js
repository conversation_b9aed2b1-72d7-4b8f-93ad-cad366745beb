import winston from 'winston';
import path from 'path';
import fs from 'fs';

class Logger {
  constructor() {
    // Implementar Singleton para evitar múltiplas instâncias
    if (Logger.instance) {
      return Logger.instance;
    }

    this.logLevel = process.env.LOG_LEVEL || 'info';
    this.logFile = process.env.LOG_FILE || 'logs/whatsapp-backend.log';
    this.logDir = path.dirname(this.logFile);

    // Criar diretório de logs se não existir
    this.ensureLogDirectory();

    // Configurar winston
    this.logger = winston.createLogger({
      level: this.logLevel,
      format: winston.format.combine(
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss'
        }),
        winston.format.errors({ stack: true }),
        winston.format.printf(({ level, message, timestamp, stack }) => {
          if (stack) {
            return `${timestamp} [${level.toUpperCase()}]: ${message}\n${stack}`;
          }
          return `${timestamp} [${level.toUpperCase()}]: ${message}`;
        })
      ),
      transports: [
        // Console transport
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
            winston.format.printf(({ level, message, timestamp }) => {
              return `${timestamp} ${level}: ${message}`;
            })
          )
        }),
        
        // File transport
        new winston.transports.File({
          filename: this.logFile,
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
          tailable: true
        }),
        
        // Error file transport
        new winston.transports.File({
          filename: path.join(this.logDir, 'error.log'),
          level: 'error',
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
          tailable: true
        })
      ],
      
      // Handle uncaught exceptions
      exceptionHandlers: [
        new winston.transports.File({
          filename: path.join(this.logDir, 'exceptions.log')
        })
      ],
      
      // Handle unhandled promise rejections
      rejectionHandlers: [
        new winston.transports.File({
          filename: path.join(this.logDir, 'rejections.log')
        })
      ]
    });

    // Adicionar metadata padrão
    this.defaultMeta = {
      service: 'whatsapp-backend',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    };

    // Aumentar limite de listeners para evitar warnings
    process.setMaxListeners(20);

    // Armazenar instância singleton
    Logger.instance = this;
  }

  ensureLogDirectory() {
    try {
      if (!fs.existsSync(this.logDir)) {
        fs.mkdirSync(this.logDir, { recursive: true });
      }
    } catch (error) {
      console.error('Erro ao criar diretório de logs:', error);
    }
  }

  // Métodos de logging
  error(message, meta = {}) {
    this.logger.error(message, { ...this.defaultMeta, ...meta });
  }

  warn(message, meta = {}) {
    this.logger.warn(message, { ...this.defaultMeta, ...meta });
  }

  info(message, meta = {}) {
    this.logger.info(message, { ...this.defaultMeta, ...meta });
  }

  debug(message, meta = {}) {
    this.logger.debug(message, { ...this.defaultMeta, ...meta });
  }

  verbose(message, meta = {}) {
    this.logger.verbose(message, { ...this.defaultMeta, ...meta });
  }

  // Métodos específicos para WhatsApp
  whatsappMessage(direction, from, to, message, meta = {}) {
    this.info(`WhatsApp ${direction}: ${from} -> ${to}`, {
      ...meta,
      type: 'whatsapp_message',
      direction,
      from,
      to,
      message: message.substring(0, 100) + (message.length > 100 ? '...' : '')
    });
  }

  whatsappConnection(status, sessionName, meta = {}) {
    this.info(`WhatsApp Connection: ${status}`, {
      ...meta,
      type: 'whatsapp_connection',
      status,
      sessionName
    });
  }

  ragRequest(query, response, processingTime, meta = {}) {
    this.info(`RAG Request processed in ${processingTime}ms`, {
      ...meta,
      type: 'rag_request',
      query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
      responseLength: response.length,
      processingTime
    });
  }

  apiRequest(method, url, statusCode, responseTime, meta = {}) {
    this.info(`API ${method} ${url} - ${statusCode} (${responseTime}ms)`, {
      ...meta,
      type: 'api_request',
      method,
      url,
      statusCode,
      responseTime
    });
  }

  // Método para logging estruturado
  structured(level, message, data = {}) {
    this.logger.log(level, message, {
      ...this.defaultMeta,
      ...data,
      timestamp: new Date().toISOString()
    });
  }

  // Método para logging de performance
  performance(operation, duration, meta = {}) {
    this.info(`Performance: ${operation} completed in ${duration}ms`, {
      ...meta,
      type: 'performance',
      operation,
      duration
    });
  }

  // Método para logging de segurança
  security(event, details, meta = {}) {
    this.warn(`Security Event: ${event}`, {
      ...meta,
      type: 'security',
      event,
      details
    });
  }

  // Método para criar child logger com contexto
  child(context = {}) {
    return {
      error: (message, meta = {}) => this.error(message, { ...context, ...meta }),
      warn: (message, meta = {}) => this.warn(message, { ...context, ...meta }),
      info: (message, meta = {}) => this.info(message, { ...context, ...meta }),
      debug: (message, meta = {}) => this.debug(message, { ...context, ...meta }),
      verbose: (message, meta = {}) => this.verbose(message, { ...context, ...meta })
    };
  }

  // Método para obter estatísticas de logs
  async getLogStats() {
    try {
      const logFiles = [
        this.logFile,
        path.join(this.logDir, 'error.log'),
        path.join(this.logDir, 'exceptions.log'),
        path.join(this.logDir, 'rejections.log')
      ];

      const stats = {};

      for (const file of logFiles) {
        try {
          const stat = fs.statSync(file);
          const fileName = path.basename(file);
          stats[fileName] = {
            size: stat.size,
            created: stat.birthtime,
            modified: stat.mtime,
            sizeFormatted: this.formatBytes(stat.size)
          };
        } catch (error) {
          // Arquivo não existe
          const fileName = path.basename(file);
          stats[fileName] = {
            size: 0,
            exists: false
          };
        }
      }

      return stats;
    } catch (error) {
      this.error('Erro ao obter estatísticas de logs:', error);
      return {};
    }
  }

  // Método para limpar logs antigos
  async cleanOldLogs(daysToKeep = 30) {
    try {
      const logFiles = fs.readdirSync(this.logDir);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      let cleanedCount = 0;

      for (const file of logFiles) {
        const filePath = path.join(this.logDir, file);
        const stat = fs.statSync(filePath);

        if (stat.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          cleanedCount++;
          this.info(`Log antigo removido: ${file}`);
        }
      }

      this.info(`Limpeza de logs concluída: ${cleanedCount} arquivos removidos`);
      return cleanedCount;

    } catch (error) {
      this.error('Erro ao limpar logs antigos:', error);
      return 0;
    }
  }

  // Método para rotacionar logs manualmente
  async rotateLogs() {
    try {
      // Winston já faz rotação automática, mas podemos forçar
      this.logger.close();
      
      // Recriar logger
      this.logger = winston.createLogger({
        level: this.logLevel,
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.errors({ stack: true }),
          winston.format.json()
        ),
        transports: [
          new winston.transports.Console(),
          new winston.transports.File({ filename: this.logFile })
        ]
      });

      this.info('Logs rotacionados com sucesso');
      return true;

    } catch (error) {
      console.error('Erro ao rotacionar logs:', error);
      return false;
    }
  }

  // Utilitário para formatar bytes
  formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  // Método para obter configuração atual
  getConfig() {
    return {
      level: this.logLevel,
      file: this.logFile,
      directory: this.logDir,
      environment: process.env.NODE_ENV || 'development'
    };
  }
}

// Propriedade estática para singleton
Logger.instance = null;

// Instância singleton do logger
let loggerInstance = null;

// Função para criar/obter instância do logger
export function createLogger(context = '') {
  if (!loggerInstance) {
    loggerInstance = new Logger();
  }

  if (context) {
    return loggerInstance.child({ context });
  }

  return loggerInstance;
}

// Exportar instância padrão
export const logger = createLogger();

// Exportar classe para uso avançado
export { Logger };
